<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPT to XML Converter</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-zone {
            border: 3px dashed #cbd5e0;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            background: #f8fafc;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 30px;
        }

        .upload-zone:hover,
        .upload-zone.dragover {
            border-color: #667eea;
            background: #edf2f7;
            transform: translateY(-2px);
        }

        .upload-icon {
            font-size: 4rem;
            color: #a0aec0;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2rem;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #718096;
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .progress-container {
            display: none;
            margin: 30px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #4a5568;
            font-weight: 500;
        }

        .results-container {
            display: none;
            margin-top: 30px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-title {
            font-size: 1.5rem;
            color: #2d3748;
            font-weight: 600;
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .xml-preview {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .error-container {
            display: none;
            background: #fed7d7;
            border: 1px solid #feb2b2;
            color: #c53030;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .error-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .success-container {
            display: none;
            background: #c6f6d5;
            border: 1px solid #9ae6b4;
            color: #22543d;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .info-section {
            background: #ebf8ff;
            border: 1px solid #bee3f8;
            color: #2c5282;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .info-title {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .info-list {
            list-style: none;
            padding-left: 0;
        }

        .info-list li {
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }

        .info-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #3182ce;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 20px;
            }

            .upload-zone {
                padding: 40px 15px;
            }

            .results-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RPT to XML Converter</h1>
            <p>Convert Crystal Reports (.rpt) files to structured XML format</p>
        </div>

        <div class="main-content">
            <div class="info-section">
                <div class="info-title">What this tool does:</div>
                <ul class="info-list">
                    <li>Parses Crystal Reports binary (.rpt) files</li>
                    <li>Extracts report metadata, fields, formulas, and database connections</li>
                    <li>Generates clean, structured XML output</li>
                    <li>Works entirely in your browser - no data sent to servers</li>
                    <li>Supports files up to 50MB in size</li>
                </ul>
            </div>

            <div class="upload-zone" id="uploadZone">
                <div class="upload-icon">📄</div>
                <div class="upload-text">Drop your .rpt file here or click to browse</div>
                <div class="upload-subtext">Supports Crystal Reports files (.rpt) up to 50MB</div>
                <input type="file" id="fileInput" class="file-input" accept=".rpt" />
            </div>

            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Initializing...</div>
            </div>

            <div class="error-container" id="errorContainer">
                <div class="error-title">Error</div>
                <div id="errorMessage"></div>
            </div>

            <div class="success-container" id="successContainer">
                <div class="error-title">Success!</div>
                <div>Your RPT file has been successfully converted to XML.</div>
            </div>

            <div class="results-container" id="resultsContainer">
                <div class="results-header">
                    <div class="results-title">Conversion Results</div>
                    <a href="#" class="download-btn" id="downloadBtn">
                        <span>📥</span>
                        Download XML
                    </a>
                </div>
                <div class="xml-preview" id="xmlPreview"></div>
            </div>
        </div>
    </div>

    <script>
        class RPTParser {
            constructor() {
                this.dataView = null;
                this.offset = 0;
                this.fileSize = 0;
                this.parsedData = {
                    metadata: {},
                    database: {},
                    fields: [],
                    formulas: [],
                    sections: [],
                    layout: {}
                };
            }

            async parseFile(file) {
                try {
                    const arrayBuffer = await this.readFileAsArrayBuffer(file);
                    this.dataView = new DataView(arrayBuffer);
                    this.fileSize = arrayBuffer.byteLength;
                    this.offset = 0;

                    // Validate RPT file signature
                    if (!this.validateRPTSignature()) {
                        throw new Error('Invalid RPT file format. File does not contain valid Crystal Reports signature.');
                    }

                    // Parse file sections
                    await this.parseFileHeader();
                    await this.parseDatabaseInfo();
                    await this.parseFields();
                    await this.parseFormulas();
                    await this.parseLayout();

                    return this.parsedData;
                } catch (error) {
                    console.error('RPT parsing error:', error);
                    throw error;
                }
            }

            readFileAsArrayBuffer(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = () => reject(new Error('Failed to read file'));
                    reader.readAsArrayBuffer(file);
                });
            }

            validateRPTSignature() {
                // Check for common RPT file signatures and patterns
                const signature = this.readBytes(16);
                this.offset = 0;

                // Look for Crystal Reports indicators in the first few bytes
                // RPT files often contain specific byte patterns or version info
                let hasValidPattern = false;

                // Check for common patterns found in RPT files
                for (let i = 0; i < Math.min(1024, this.fileSize - 4); i++) {
                    const bytes = new Uint8Array(4);
                    for (let j = 0; j < 4; j++) {
                        if (i + j < this.fileSize) {
                            bytes[j] = this.dataView.getUint8(i + j);
                        }
                    }

                    // Look for text patterns that might indicate Crystal Reports
                    const text = this.extractStringAt(i, 20);
                    if (text && (
                        text.includes('Crystal') ||
                        text.includes('Report') ||
                        text.includes('CRPE') ||
                        text.includes('Database') ||
                        text.includes('Field') ||
                        text.includes('Formula')
                    )) {
                        hasValidPattern = true;
                        break;
                    }
                }

                // Also check file size - RPT files are typically substantial
                if (this.fileSize < 100) {
                    throw new Error('File too small to be a valid RPT file');
                }

                return hasValidPattern || this.fileSize > 1000; // Accept if patterns found or reasonable size
            }

            async parseFileHeader() {
                this.updateProgress(10, 'Parsing file header...');

                // Reset offset and scan for metadata
                this.offset = 0;

                // Scan the file for metadata patterns
                const metadata = {
                    fileSize: this.fileSize,
                    version: this.detectVersion(),
                    createdDate: this.findTimestamp() || new Date().toISOString(),
                    modifiedDate: this.findTimestamp() || new Date().toISOString(),
                    title: this.findReportTitle() || 'Unknown Report',
                    author: this.findAuthor() || 'Unknown',
                    description: 'Extracted from Crystal Reports RPT file',
                    encoding: 'Binary',
                    reportType: 'Crystal Reports'
                };

                this.parsedData.metadata = metadata;
            }

            findReportTitle() {
                // Scan for potential report titles
                for (let i = 0; i < Math.min(2048, this.fileSize - 50); i++) {
                    const text = this.extractStringAt(i, 50);
                    if (text && text.length > 5 && text.length < 100) {
                        // Look for title-like patterns
                        if (this.isLikelyTitle(text)) {
                            return text;
                        }
                    }
                }
                return null;
            }

            isLikelyTitle(text) {
                // Check if text looks like a report title
                const titlePatterns = [
                    /report/i,
                    /summary/i,
                    /analysis/i,
                    /statement/i,
                    /invoice/i,
                    /list/i
                ];

                // Must be reasonable length and contain title-like words
                if (text.length < 5 || text.length > 80) return false;

                // Should not contain too many special characters
                const specialCharCount = (text.match(/[^a-zA-Z0-9\s\-_]/g) || []).length;
                if (specialCharCount > text.length * 0.3) return false;

                // Check for title patterns
                return titlePatterns.some(pattern => pattern.test(text));
            }

            findAuthor() {
                // Look for author information
                const authorPatterns = ['Author:', 'Created by:', 'Designer:', 'User:'];

                for (let i = 0; i < Math.min(2048, this.fileSize - 100); i++) {
                    const text = this.extractStringAt(i, 100);
                    if (text) {
                        for (const pattern of authorPatterns) {
                            const index = text.indexOf(pattern);
                            if (index >= 0) {
                                const authorText = text.substring(index + pattern.length).trim();
                                const author = authorText.split(/[\r\n\0]/)[0].trim();
                                if (author.length > 0 && author.length < 50) {
                                    return author;
                                }
                            }
                        }
                    }
                }
                return null;
            }

            findTimestamp() {
                // Look for timestamp patterns in the file
                for (let i = 0; i < Math.min(2048, this.fileSize - 20); i++) {
                    // Check for common timestamp formats
                    const text = this.extractStringAt(i, 20);
                    if (text) {
                        // Look for date patterns like YYYY-MM-DD or MM/DD/YYYY
                        const dateMatch = text.match(/(\d{4}[-/]\d{1,2}[-/]\d{1,2})|(\d{1,2}[-/]\d{1,2}[-/]\d{4})/);
                        if (dateMatch) {
                            try {
                                const date = new Date(dateMatch[0]);
                                if (!isNaN(date.getTime()) && date.getFullYear() > 1990 && date.getFullYear() < 2030) {
                                    return date.toISOString();
                                }
                            } catch (e) {
                                // Invalid date, continue searching
                            }
                        }
                    }
                }
                return null;
            }

            detectVersion() {
                // Try to detect Crystal Reports version from file structure and content
                const versionPatterns = [
                    { pattern: 'Crystal Reports 2020', version: '2020' },
                    { pattern: 'Crystal Reports 2016', version: '2016' },
                    { pattern: 'Crystal Reports XI', version: 'XI' },
                    { pattern: 'Crystal Reports 10', version: '10' },
                    { pattern: 'Crystal Reports 9', version: '9' },
                    { pattern: 'Crystal Reports 8.5', version: '8.5' },
                    { pattern: 'CRPE32', version: 'Legacy' },
                    { pattern: 'CrystalRuntime', version: 'Runtime' }
                ];

                // Scan first 4KB for version information
                for (let i = 0; i < Math.min(4096, this.fileSize - 50); i++) {
                    const text = this.extractStringAt(i, 50);
                    if (text) {
                        for (const versionInfo of versionPatterns) {
                            if (text.includes(versionInfo.pattern)) {
                                return versionInfo.version;
                            }
                        }
                    }
                }

                // Try to guess version based on file structure patterns
                if (this.fileSize > 1000000) return 'Modern (2016+)';
                if (this.fileSize > 500000) return 'Recent (XI-2016)';
                if (this.fileSize > 100000) return 'Legacy (8-10)';

                return 'Unknown Version';
            }

            async parseDatabaseInfo() {
                this.updateProgress(25, 'Parsing database connections...');
                
                // Look for database connection strings and table information
                this.parsedData.database = {
                    connections: this.findDatabaseConnections(),
                    tables: this.findTableReferences(),
                    queries: this.findSQLQueries()
                };
            }

            findDatabaseConnections() {
                const connections = [];
                const foundConnections = new Set(); // Avoid duplicates

                // Enhanced search for database connection patterns
                const connectionPatterns = [
                    { pattern: 'Data Source=', type: 'SQL Server' },
                    { pattern: 'Server=', type: 'SQL Server' },
                    { pattern: 'Database=', type: 'SQL Database' },
                    { pattern: 'Provider=', type: 'OLE DB' },
                    { pattern: 'DRIVER=', type: 'ODBC' },
                    { pattern: 'DSN=', type: 'ODBC DSN' },
                    { pattern: 'UID=', type: 'Database Login' },
                    { pattern: 'PWD=', type: 'Database Password' },
                    { pattern: 'Integrated Security=', type: 'Windows Auth' },
                    { pattern: 'Initial Catalog=', type: 'SQL Server' },
                    { pattern: 'Host=', type: 'Database Host' },
                    { pattern: 'Port=', type: 'Database Port' },
                    { pattern: 'Oracle', type: 'Oracle Database' },
                    { pattern: 'MySQL', type: 'MySQL Database' },
                    { pattern: 'PostgreSQL', type: 'PostgreSQL' },
                    { pattern: 'Access Database', type: 'MS Access' }
                ];

                // Scan file for connection strings
                for (let i = 0; i < this.fileSize - 200; i += 4) { // Skip bytes for performance
                    const text = this.extractStringAt(i, 200);
                    if (text && text.length > 10) {
                        for (const connPattern of connectionPatterns) {
                            if (text.includes(connPattern.pattern)) {
                                // Extract the full connection string
                                const startIndex = text.indexOf(connPattern.pattern);
                                let connectionString = text.substring(startIndex);

                                // Truncate at common delimiters
                                const delimiters = ['\0', '\r', '\n', ';'];
                                for (const delimiter of delimiters) {
                                    const delimIndex = connectionString.indexOf(delimiter);
                                    if (delimIndex > 0) {
                                        connectionString = connectionString.substring(0, delimIndex);
                                    }
                                }

                                // Avoid duplicates
                                const connectionKey = `${connPattern.type}:${connectionString.substring(0, 50)}`;
                                if (!foundConnections.has(connectionKey) && connectionString.length > 10) {
                                    foundConnections.add(connectionKey);
                                    connections.push({
                                        type: connPattern.type,
                                        connectionString: connectionString.substring(0, 300),
                                        pattern: connPattern.pattern,
                                        offset: i + startIndex
                                    });
                                }
                                break;
                            }
                        }
                    }
                }

                return connections.length > 0 ? connections : [{
                    type: 'Unknown',
                    connectionString: 'No database connections found in RPT file',
                    pattern: 'N/A',
                    offset: 0
                }];
            }

            findTableReferences() {
                const tables = [];
                
                // Look for table name patterns
                const tablePatterns = ['SELECT', 'FROM', 'TABLE', 'VIEW'];
                
                for (let i = 0; i < this.fileSize - 50; i++) {
                    const text = this.extractStringAt(i, 50);
                    if (text) {
                        for (const pattern of tablePatterns) {
                            if (text.toUpperCase().includes(pattern)) {
                                tables.push({
                                    name: this.extractTableName(text),
                                    type: 'Table',
                                    offset: i
                                });
                                break;
                            }
                        }
                    }
                }

                return tables.length > 0 ? tables : [{
                    name: 'Unknown Table',
                    type: 'Table',
                    offset: 0
                }];
            }

            extractTableName(text) {
                // Extract table name from SQL-like text
                const words = text.split(/\s+/);
                for (let i = 0; i < words.length - 1; i++) {
                    if (words[i].toUpperCase() === 'FROM') {
                        return words[i + 1] || 'Unknown';
                    }
                }
                return 'Unknown';
            }

            findSQLQueries() {
                const queries = [];
                
                // Look for SQL query patterns
                for (let i = 0; i < this.fileSize - 200; i++) {
                    const text = this.extractStringAt(i, 200);
                    if (text && text.toUpperCase().includes('SELECT')) {
                        queries.push({
                            query: text.substring(0, 150),
                            offset: i
                        });
                    }
                }

                return queries.length > 0 ? queries : [{
                    query: 'No SQL queries found',
                    offset: 0
                }];
            }

            async parseFields() {
                this.updateProgress(50, 'Parsing field definitions...');
                
                // Extract field information
                this.parsedData.fields = this.findFields();
            }

            findFields() {
                const fields = [];
                const foundFields = new Set(); // Avoid duplicates

                // Enhanced field detection patterns
                const fieldPatterns = [
                    { pattern: /\{[^}]+\}/g, type: 'Crystal Field' },
                    { pattern: /@[A-Za-z_][A-Za-z0-9_]*/g, type: 'Parameter Field' },
                    { pattern: /\b[A-Za-z_][A-Za-z0-9_]*\.[A-Za-z_][A-Za-z0-9_]*/g, type: 'Table Field' }
                ];

                // Scan file for field patterns
                for (let i = 0; i < this.fileSize - 150; i += 8) { // Skip bytes for performance
                    const text = this.extractStringAt(i, 150);
                    if (text && text.length > 3) {

                        // Check each field pattern
                        for (const fieldPattern of fieldPatterns) {
                            const matches = text.match(fieldPattern.pattern);
                            if (matches) {
                                for (const match of matches) {
                                    let fieldName = match;

                                    // Clean up field name
                                    if (fieldName.startsWith('{') && fieldName.endsWith('}')) {
                                        fieldName = fieldName.slice(1, -1);
                                    }

                                    // Validate field name
                                    if (this.isValidFieldName(fieldName) && !foundFields.has(fieldName)) {
                                        foundFields.add(fieldName);

                                        fields.push({
                                            name: fieldName,
                                            type: this.guessFieldType(text, fieldName),
                                            source: this.guessFieldSource(fieldName, fieldPattern.type),
                                            category: fieldPattern.type,
                                            offset: i + text.indexOf(match),
                                            context: text.substring(Math.max(0, text.indexOf(match) - 20),
                                                                  Math.min(text.length, text.indexOf(match) + match.length + 20))
                                        });
                                    }
                                }
                            }
                        }

                        // Also look for SQL column references
                        const sqlColumnPattern = /\b(SELECT|FROM|WHERE|ORDER BY|GROUP BY)\s+([A-Za-z_][A-Za-z0-9_]*)/gi;
                        const sqlMatches = text.match(sqlColumnPattern);
                        if (sqlMatches) {
                            for (const match of sqlMatches) {
                                const parts = match.trim().split(/\s+/);
                                if (parts.length >= 2) {
                                    const columnName = parts[1];
                                    if (this.isValidFieldName(columnName) && !foundFields.has(columnName)) {
                                        foundFields.add(columnName);
                                        fields.push({
                                            name: columnName,
                                            type: 'Unknown',
                                            source: 'SQL Query',
                                            category: 'SQL Column',
                                            offset: i + text.indexOf(match),
                                            context: match
                                        });
                                    }
                                }
                            }
                        }
                    }
                }

                return fields.length > 0 ? fields : [{
                    name: 'NoFieldsFound',
                    type: 'Unknown',
                    source: 'N/A',
                    category: 'Placeholder',
                    offset: 0,
                    context: 'No fields detected in RPT file'
                }];
            }

            isValidFieldName(name) {
                // Validate field name
                if (!name || name.length < 1 || name.length > 100) return false;

                // Should not be all special characters
                if (!/[A-Za-z0-9]/.test(name)) return false;

                // Should not contain too many special characters
                const specialCharCount = (name.match(/[^A-Za-z0-9_\.]/g) || []).length;
                if (specialCharCount > name.length * 0.5) return false;

                // Exclude common false positives
                const excludePatterns = [
                    /^\d+$/,  // Pure numbers
                    /^[^A-Za-z]/,  // Starting with non-letter
                    /\s{2,}/,  // Multiple spaces
                    /^(and|or|not|if|then|else|end|begin)$/i  // SQL keywords
                ];

                return !excludePatterns.some(pattern => pattern.test(name));
            }

            guessFieldSource(fieldName, category) {
                if (category === 'Parameter Field') return 'Parameter';
                if (category === 'SQL Column') return 'Database';
                if (fieldName.includes('.')) return 'Database Table';
                if (fieldName.startsWith('@')) return 'Parameter';
                return 'Database';
            }

            extractFieldName(text) {
                const match = text.match(/\{([^}]+)\}/);
                return match ? match[1] : null;
            }

            guessFieldType(text, fieldName = '') {
                const lowerText = text.toLowerCase();
                const lowerFieldName = fieldName.toLowerCase();

                // Date type indicators
                if (lowerText.includes('date') || lowerFieldName.includes('date') ||
                    lowerFieldName.includes('time') || lowerFieldName.includes('created') ||
                    lowerFieldName.includes('modified') || lowerFieldName.includes('updated')) {
                    return 'DateTime';
                }

                // Numeric type indicators
                if (lowerText.includes('number') || lowerText.includes('int') ||
                    lowerText.includes('decimal') || lowerText.includes('float') ||
                    lowerFieldName.includes('amount') || lowerFieldName.includes('price') ||
                    lowerFieldName.includes('cost') || lowerFieldName.includes('total') ||
                    lowerFieldName.includes('count') || lowerFieldName.includes('qty') ||
                    lowerFieldName.includes('quantity') || lowerFieldName.includes('id')) {
                    return 'Number';
                }

                // Boolean type indicators
                if (lowerText.includes('bool') || lowerText.includes('flag') ||
                    lowerFieldName.includes('is') || lowerFieldName.includes('has') ||
                    lowerFieldName.includes('active') || lowerFieldName.includes('enabled')) {
                    return 'Boolean';
                }

                // Currency type indicators
                if (lowerFieldName.includes('currency') || lowerFieldName.includes('money') ||
                    lowerFieldName.includes('salary') || lowerFieldName.includes('wage')) {
                    return 'Currency';
                }

                // Text/String is default
                return 'String';
            }

            async parseFormulas() {
                this.updateProgress(75, 'Parsing formulas...');
                
                // Extract formula information
                this.parsedData.formulas = this.findFormulas();
            }

            findFormulas() {
                const formulas = [];
                const foundFormulas = new Set(); // Avoid duplicates

                // Enhanced formula detection patterns
                const formulaPatterns = [
                    { pattern: /\bIf\s+.+\s+Then\s+.+/gi, type: 'Conditional' },
                    { pattern: /\bSelect\s+Case\s+.+/gi, type: 'Case Statement' },
                    { pattern: /\bSum\s*\([^)]+\)/gi, type: 'Aggregate' },
                    { pattern: /\bCount\s*\([^)]+\)/gi, type: 'Aggregate' },
                    { pattern: /\bAvg\s*\([^)]+\)/gi, type: 'Aggregate' },
                    { pattern: /\bMax\s*\([^)]+\)/gi, type: 'Aggregate' },
                    { pattern: /\bMin\s*\([^)]+\)/gi, type: 'Aggregate' },
                    { pattern: /\b[A-Za-z_][A-Za-z0-9_]*\s*:=\s*.+/g, type: 'Assignment' },
                    { pattern: /\{[^}]+\}\s*[\+\-\*\/]\s*\{[^}]+\}/g, type: 'Arithmetic' },
                    { pattern: /\bToText\s*\([^)]+\)/gi, type: 'Conversion' },
                    { pattern: /\bToNumber\s*\([^)]+\)/gi, type: 'Conversion' },
                    { pattern: /\bToDate\s*\([^)]+\)/gi, type: 'Conversion' },
                    { pattern: /\bFormat\s*\([^)]+\)/gi, type: 'Formatting' },
                    { pattern: /\bLen\s*\([^)]+\)/gi, type: 'String Function' },
                    { pattern: /\bTrim\s*\([^)]+\)/gi, type: 'String Function' },
                    { pattern: /\bUpper\s*\([^)]+\)/gi, type: 'String Function' },
                    { pattern: /\bLower\s*\([^)]+\)/gi, type: 'String Function' }
                ];

                // Scan file for formula patterns
                for (let i = 0; i < this.fileSize - 300; i += 16) { // Skip bytes for performance
                    const text = this.extractStringAt(i, 300);
                    if (text && text.length > 5) {

                        // Check each formula pattern
                        for (const formulaPattern of formulaPatterns) {
                            const matches = text.match(formulaPattern.pattern);
                            if (matches) {
                                for (const match of matches) {
                                    const cleanMatch = match.trim();

                                    if (cleanMatch.length > 5 && cleanMatch.length < 500 &&
                                        !foundFormulas.has(cleanMatch)) {
                                        foundFormulas.add(cleanMatch);

                                        formulas.push({
                                            name: this.generateFormulaName(cleanMatch, formulas.length),
                                            expression: cleanMatch,
                                            type: formulaPattern.type,
                                            offset: i + text.indexOf(match),
                                            context: text.substring(
                                                Math.max(0, text.indexOf(match) - 30),
                                                Math.min(text.length, text.indexOf(match) + match.length + 30)
                                            )
                                        });
                                    }
                                }
                            }
                        }

                        // Also look for general assignment patterns
                        const assignmentPattern = /([A-Za-z_][A-Za-z0-9_]*)\s*=\s*([^;\r\n]+)/g;
                        const assignmentMatches = text.match(assignmentPattern);
                        if (assignmentMatches) {
                            for (const match of assignmentMatches) {
                                const cleanMatch = match.trim();
                                if (cleanMatch.length > 5 && cleanMatch.length < 200 &&
                                    !foundFormulas.has(cleanMatch)) {
                                    foundFormulas.add(cleanMatch);

                                    formulas.push({
                                        name: this.extractFormulaNameFromAssignment(cleanMatch),
                                        expression: cleanMatch,
                                        type: 'Assignment',
                                        offset: i + text.indexOf(match),
                                        context: cleanMatch
                                    });
                                }
                            }
                        }
                    }
                }

                return formulas.length > 0 ? formulas : [{
                    name: 'NoFormulasFound',
                    expression: 'No formulas detected in RPT file',
                    type: 'N/A',
                    offset: 0,
                    context: 'No formula patterns found'
                }];
            }

            generateFormulaName(expression, index) {
                // Try to extract a meaningful name from the formula
                if (expression.toLowerCase().includes('sum')) return `SumFormula${index + 1}`;
                if (expression.toLowerCase().includes('count')) return `CountFormula${index + 1}`;
                if (expression.toLowerCase().includes('if')) return `ConditionalFormula${index + 1}`;
                if (expression.toLowerCase().includes('format')) return `FormatFormula${index + 1}`;
                if (expression.toLowerCase().includes('date')) return `DateFormula${index + 1}`;

                return `Formula${index + 1}`;
            }

            extractFormulaNameFromAssignment(assignment) {
                const match = assignment.match(/^([A-Za-z_][A-Za-z0-9_]*)\s*=/);
                return match ? match[1] : 'UnnamedFormula';
            }

            async parseLayout() {
                this.updateProgress(90, 'Parsing layout information...');

                // Extract layout and section information
                this.parsedData.layout = this.extractLayoutInfo();
                this.parsedData.sections = this.extractSections();
                this.parsedData.groups = this.extractGroups();
                this.parsedData.sorting = this.extractSorting();
            }

            extractLayoutInfo() {
                const layout = {
                    pageSize: 'Letter',
                    orientation: 'Portrait',
                    margins: { top: 0.5, bottom: 0.5, left: 0.5, right: 0.5 },
                    units: 'Inches',
                    paperSource: 'Auto'
                };

                // Look for layout-related information in the file
                for (let i = 0; i < Math.min(4096, this.fileSize - 100); i += 8) {
                    const text = this.extractStringAt(i, 100);
                    if (text) {
                        // Check for page size indicators
                        if (text.toLowerCase().includes('a4')) layout.pageSize = 'A4';
                        if (text.toLowerCase().includes('legal')) layout.pageSize = 'Legal';
                        if (text.toLowerCase().includes('landscape')) layout.orientation = 'Landscape';

                        // Look for margin information (this would require more specific parsing)
                        const marginMatch = text.match(/margin[s]?\s*[:=]\s*([0-9.]+)/i);
                        if (marginMatch) {
                            const margin = parseFloat(marginMatch[1]);
                            if (margin > 0 && margin < 5) {
                                layout.margins = { top: margin, bottom: margin, left: margin, right: margin };
                            }
                        }
                    }
                }

                return layout;
            }

            extractSections() {
                const sections = [];
                const sectionNames = [
                    'Page Header', 'Report Header', 'Group Header', 'Details',
                    'Group Footer', 'Report Footer', 'Page Footer'
                ];

                // Look for section information
                for (const sectionName of sectionNames) {
                    const section = {
                        name: sectionName,
                        height: this.estimateSectionHeight(sectionName),
                        objects: this.findSectionObjects(sectionName),
                        visible: true,
                        canGrow: false,
                        canShrink: false,
                        backgroundColor: 'Transparent'
                    };
                    sections.push(section);
                }

                return sections;
            }

            estimateSectionHeight(sectionName) {
                // Estimate section heights based on typical Crystal Reports layouts
                const heightMap = {
                    'Page Header': 0.5,
                    'Report Header': 1.0,
                    'Group Header': 0.3,
                    'Details': 0.25,
                    'Group Footer': 0.3,
                    'Report Footer': 0.5,
                    'Page Footer': 0.5
                };
                return heightMap[sectionName] || 0.25;
            }

            findSectionObjects(sectionName) {
                const objects = [];

                // Look for objects that might be in this section
                for (let i = 0; i < Math.min(8192, this.fileSize - 100); i += 16) {
                    const text = this.extractStringAt(i, 100);
                    if (text && text.length > 3) {
                        // Look for text objects
                        if (this.isLikelyTextObject(text)) {
                            objects.push({
                                type: 'Text',
                                content: text.substring(0, 50),
                                x: 0,
                                y: 0,
                                width: 2,
                                height: 0.2,
                                font: 'Arial',
                                fontSize: 10,
                                offset: i
                            });
                        }

                        // Look for field objects
                        if (text.includes('{') && text.includes('}')) {
                            const fieldName = this.extractFieldName(text);
                            if (fieldName) {
                                objects.push({
                                    type: 'Field',
                                    fieldName: fieldName,
                                    x: 0,
                                    y: 0,
                                    width: 1.5,
                                    height: 0.2,
                                    font: 'Arial',
                                    fontSize: 10,
                                    offset: i
                                });
                            }
                        }
                    }
                }

                return objects.slice(0, 10); // Limit to prevent too many objects
            }

            isLikelyTextObject(text) {
                // Check if text looks like a report text object
                if (text.length < 3 || text.length > 100) return false;

                // Should contain mostly printable characters
                const printableCount = (text.match(/[A-Za-z0-9\s\-_.,!?]/g) || []).length;
                if (printableCount < text.length * 0.7) return false;

                // Common report text patterns
                const textPatterns = [
                    /report/i, /title/i, /header/i, /footer/i, /page/i, /date/i,
                    /company/i, /address/i, /phone/i, /total/i, /summary/i
                ];

                return textPatterns.some(pattern => pattern.test(text));
            }

            extractGroups() {
                const groups = [];

                // Look for grouping information
                for (let i = 0; i < Math.min(4096, this.fileSize - 100); i += 16) {
                    const text = this.extractStringAt(i, 100);
                    if (text && (text.toLowerCase().includes('group') || text.toLowerCase().includes('sort'))) {
                        const groupField = this.extractGroupField(text);
                        if (groupField) {
                            groups.push({
                                field: groupField,
                                sortOrder: 'Ascending',
                                keepTogether: false,
                                repeatGroupHeader: false,
                                offset: i
                            });
                        }
                    }
                }

                return groups.length > 0 ? groups : [{
                    field: 'No grouping detected',
                    sortOrder: 'None',
                    keepTogether: false,
                    repeatGroupHeader: false,
                    offset: 0
                }];
            }

            extractGroupField(text) {
                // Try to extract the field being grouped on
                const fieldMatch = text.match(/\{([^}]+)\}/);
                if (fieldMatch) return fieldMatch[1];

                const wordMatch = text.match(/\b([A-Za-z_][A-Za-z0-9_]*)\b/);
                if (wordMatch && wordMatch[1].length > 2) return wordMatch[1];

                return null;
            }

            extractSorting() {
                const sorting = [];

                // Look for sorting information
                for (let i = 0; i < Math.min(4096, this.fileSize - 100); i += 16) {
                    const text = this.extractStringAt(i, 100);
                    if (text && text.toLowerCase().includes('order by')) {
                        const sortField = this.extractSortField(text);
                        if (sortField) {
                            sorting.push({
                                field: sortField,
                                direction: text.toLowerCase().includes('desc') ? 'Descending' : 'Ascending',
                                offset: i
                            });
                        }
                    }
                }

                return sorting.length > 0 ? sorting : [{
                    field: 'No sorting detected',
                    direction: 'None',
                    offset: 0
                }];
            }

            extractSortField(text) {
                // Extract field name from ORDER BY clause
                const orderByMatch = text.match(/order\s+by\s+([A-Za-z_][A-Za-z0-9_]*)/i);
                return orderByMatch ? orderByMatch[1] : null;
            }

            // Utility methods
            readBytes(count) {
                if (this.offset + count > this.fileSize) {
                    return new Uint8Array(0);
                }
                const bytes = new Uint8Array(count);
                for (let i = 0; i < count; i++) {
                    bytes[i] = this.dataView.getUint8(this.offset + i);
                }
                this.offset += count;
                return bytes;
            }

            readUint32() {
                if (this.offset + 4 > this.fileSize) return 0;
                const value = this.dataView.getUint32(this.offset, true); // little-endian
                this.offset += 4;
                return value;
            }

            readUint16() {
                if (this.offset + 2 > this.fileSize) return 0;
                const value = this.dataView.getUint16(this.offset, true); // little-endian
                this.offset += 2;
                return value;
            }

            extractString(maxLength = 100) {
                let result = '';
                let nullCount = 0;
                
                for (let i = 0; i < maxLength && this.offset < this.fileSize; i++) {
                    const byte = this.dataView.getUint8(this.offset++);
                    
                    if (byte === 0) {
                        nullCount++;
                        if (nullCount > 2) break; // Stop after multiple nulls
                        continue;
                    }
                    
                    nullCount = 0;
                    
                    if (byte >= 32 && byte <= 126) { // Printable ASCII
                        result += String.fromCharCode(byte);
                    }
                }
                
                return result.trim();
            }

            extractStringAt(offset, maxLength = 100) {
                let result = '';
                let nullCount = 0;
                
                for (let i = 0; i < maxLength && offset + i < this.fileSize; i++) {
                    const byte = this.dataView.getUint8(offset + i);
                    
                    if (byte === 0) {
                        nullCount++;
                        if (nullCount > 2) break;
                        continue;
                    }
                    
                    nullCount = 0;
                    
                    if (byte >= 32 && byte <= 126) {
                        result += String.fromCharCode(byte);
                    }
                }
                
                return result.trim();
            }

            updateProgress(percent, message) {
                const event = new CustomEvent('parseProgress', {
                    detail: { percent, message }
                });
                document.dispatchEvent(event);
            }
        }

        class XMLGenerator {
            constructor() {
                this.xmlContent = '';
            }

            generateXML(parsedData) {
                this.xmlContent = '';
                this.addLine('<?xml version="1.0" encoding="UTF-8"?>');
                this.addLine('<!-- Generated by RPT to XML Converter -->');
                this.addLine(`<!-- Conversion Date: ${new Date().toISOString()} -->`);
                this.addLine('<CrystalReport xmlns="http://www.crystalreports.com/schema" version="1.0">');

                this.generateMetadata(parsedData.metadata);
                this.generateDatabase(parsedData.database);
                this.generateFields(parsedData.fields);
                this.generateFormulas(parsedData.formulas);
                this.generateLayout(parsedData.layout);
                this.generateSections(parsedData.sections);
                this.generateGroups(parsedData.groups);
                this.generateSorting(parsedData.sorting);
                this.generateSummary(parsedData);

                this.addLine('</CrystalReport>');

                return this.xmlContent;
            }

            generateMetadata(metadata) {
                this.addLine('  <Metadata>');
                Object.entries(metadata).forEach(([key, value]) => {
                    this.addLine(`    <${key}>${this.escapeXML(value)}</${key}>`);
                });
                this.addLine('  </Metadata>');
            }

            generateDatabase(database) {
                this.addLine('  <Database>');

                this.addLine('    <Connections>');
                database.connections.forEach((conn, index) => {
                    this.addLine(`      <Connection id="${index + 1}">`);
                    this.addLine(`        <Type>${this.escapeXML(conn.type)}</Type>`);
                    this.addLine(`        <ConnectionString>${this.escapeXML(conn.connectionString)}</ConnectionString>`);
                    if (conn.pattern) {
                        this.addLine(`        <Pattern>${this.escapeXML(conn.pattern)}</Pattern>`);
                    }
                    this.addLine(`        <Offset>${conn.offset}</Offset>`);
                    this.addLine('      </Connection>');
                });
                this.addLine('    </Connections>');

                this.addLine('    <Tables>');
                database.tables.forEach((table, index) => {
                    this.addLine(`      <Table id="${index + 1}">`);
                    this.addLine(`        <Name>${this.escapeXML(table.name)}</Name>`);
                    this.addLine(`        <Type>${this.escapeXML(table.type)}</Type>`);
                    this.addLine(`        <Offset>${table.offset}</Offset>`);
                    this.addLine('      </Table>');
                });
                this.addLine('    </Tables>');

                this.addLine('    <Queries>');
                database.queries.forEach((query, index) => {
                    this.addLine(`      <Query id="${index + 1}">`);
                    this.addLine(`        <SQL>${this.escapeXML(query.query)}</SQL>`);
                    this.addLine(`        <Offset>${query.offset}</Offset>`);
                    this.addLine('      </Query>');
                });
                this.addLine('    </Queries>');

                this.addLine('  </Database>');
            }

            generateFields(fields) {
                this.addLine('  <Fields>');
                fields.forEach((field, index) => {
                    this.addLine(`    <Field id="${index + 1}">`);
                    this.addLine(`      <Name>${this.escapeXML(field.name)}</Name>`);
                    this.addLine(`      <Type>${this.escapeXML(field.type)}</Type>`);
                    this.addLine(`      <Source>${this.escapeXML(field.source)}</Source>`);
                    if (field.category) {
                        this.addLine(`      <Category>${this.escapeXML(field.category)}</Category>`);
                    }
                    if (field.context) {
                        this.addLine(`      <Context>${this.escapeXML(field.context)}</Context>`);
                    }
                    this.addLine(`      <Offset>${field.offset}</Offset>`);
                    this.addLine('    </Field>');
                });
                this.addLine('  </Fields>');
            }

            generateFormulas(formulas) {
                this.addLine('  <Formulas>');
                formulas.forEach((formula, index) => {
                    this.addLine(`    <Formula id="${index + 1}">`);
                    this.addLine(`      <Name>${this.escapeXML(formula.name)}</Name>`);
                    this.addLine(`      <Expression>${this.escapeXML(formula.expression)}</Expression>`);
                    this.addLine(`      <Type>${this.escapeXML(formula.type)}</Type>`);
                    if (formula.context) {
                        this.addLine(`      <Context>${this.escapeXML(formula.context)}</Context>`);
                    }
                    this.addLine(`      <Offset>${formula.offset}</Offset>`);
                    this.addLine('    </Formula>');
                });
                this.addLine('  </Formulas>');
            }

            generateLayout(layout) {
                this.addLine('  <Layout>');
                this.addLine(`    <PageSize>${this.escapeXML(layout.pageSize)}</PageSize>`);
                this.addLine(`    <Orientation>${this.escapeXML(layout.orientation)}</Orientation>`);
                this.addLine('    <Margins>');
                Object.entries(layout.margins).forEach(([key, value]) => {
                    this.addLine(`      <${key}>${value}</${key}>`);
                });
                this.addLine('    </Margins>');
                this.addLine('  </Layout>');
            }

            generateSections(sections) {
                this.addLine('  <Sections>');
                sections.forEach((section, index) => {
                    this.addLine(`    <Section id="${index + 1}">`);
                    this.addLine(`      <Name>${this.escapeXML(section.name)}</Name>`);
                    this.addLine(`      <Height>${section.height}</Height>`);
                    this.addLine(`      <Visible>${section.visible}</Visible>`);
                    this.addLine(`      <CanGrow>${section.canGrow}</CanGrow>`);
                    this.addLine(`      <CanShrink>${section.canShrink}</CanShrink>`);
                    this.addLine(`      <BackgroundColor>${this.escapeXML(section.backgroundColor)}</BackgroundColor>`);
                    this.addLine('      <Objects>');
                    section.objects.forEach((obj, objIndex) => {
                        this.addLine(`        <Object id="${objIndex + 1}">`);
                        this.addLine(`          <Type>${this.escapeXML(obj.type || 'Unknown')}</Type>`);
                        if (obj.content) {
                            this.addLine(`          <Content>${this.escapeXML(obj.content)}</Content>`);
                        }
                        if (obj.fieldName) {
                            this.addLine(`          <FieldName>${this.escapeXML(obj.fieldName)}</FieldName>`);
                        }
                        this.addLine(`          <Position>`);
                        this.addLine(`            <X>${obj.x || 0}</X>`);
                        this.addLine(`            <Y>${obj.y || 0}</Y>`);
                        this.addLine(`            <Width>${obj.width || 1}</Width>`);
                        this.addLine(`            <Height>${obj.height || 0.2}</Height>`);
                        this.addLine(`          </Position>`);
                        if (obj.font) {
                            this.addLine(`          <Font>`);
                            this.addLine(`            <Name>${this.escapeXML(obj.font)}</Name>`);
                            this.addLine(`            <Size>${obj.fontSize || 10}</Size>`);
                            this.addLine(`          </Font>`);
                        }
                        this.addLine(`          <Offset>${obj.offset || 0}</Offset>`);
                        this.addLine('        </Object>');
                    });
                    this.addLine('      </Objects>');
                    this.addLine('    </Section>');
                });
                this.addLine('  </Sections>');
            }

            generateGroups(groups) {
                this.addLine('  <Groups>');
                groups.forEach((group, index) => {
                    this.addLine(`    <Group id="${index + 1}">`);
                    this.addLine(`      <Field>${this.escapeXML(group.field)}</Field>`);
                    this.addLine(`      <SortOrder>${this.escapeXML(group.sortOrder)}</SortOrder>`);
                    this.addLine(`      <KeepTogether>${group.keepTogether}</KeepTogether>`);
                    this.addLine(`      <RepeatGroupHeader>${group.repeatGroupHeader}</RepeatGroupHeader>`);
                    this.addLine(`      <Offset>${group.offset}</Offset>`);
                    this.addLine('    </Group>');
                });
                this.addLine('  </Groups>');
            }

            generateSorting(sorting) {
                this.addLine('  <Sorting>');
                sorting.forEach((sort, index) => {
                    this.addLine(`    <SortField id="${index + 1}">`);
                    this.addLine(`      <Field>${this.escapeXML(sort.field)}</Field>`);
                    this.addLine(`      <Direction>${this.escapeXML(sort.direction)}</Direction>`);
                    this.addLine(`      <Offset>${sort.offset}</Offset>`);
                    this.addLine('    </SortField>');
                });
                this.addLine('  </Sorting>');
            }

            generateSummary(parsedData) {
                this.addLine('  <Summary>');
                this.addLine(`    <TotalFields>${parsedData.fields.length}</TotalFields>`);
                this.addLine(`    <TotalFormulas>${parsedData.formulas.length}</TotalFormulas>`);
                this.addLine(`    <TotalSections>${parsedData.sections.length}</TotalSections>`);
                this.addLine(`    <TotalConnections>${parsedData.database.connections.length}</TotalConnections>`);
                this.addLine(`    <TotalTables>${parsedData.database.tables.length}</TotalTables>`);
                this.addLine(`    <TotalGroups>${parsedData.groups ? parsedData.groups.length : 0}</TotalGroups>`);
                this.addLine(`    <FileSize>${parsedData.metadata.fileSize}</FileSize>`);
                this.addLine(`    <ReportVersion>${this.escapeXML(parsedData.metadata.version)}</ReportVersion>`);
                this.addLine('  </Summary>');
            }

            addLine(line) {
                this.xmlContent += line + '\n';
            }

            escapeXML(text) {
                if (typeof text !== 'string') {
                    text = String(text);
                }
                return text
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;');
            }
        }

        class RPTConverter {
            constructor() {
                this.parser = new RPTParser();
                this.xmlGenerator = new XMLGenerator();
                this.currentFile = null;
                this.xmlContent = '';
                
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                const uploadZone = document.getElementById('uploadZone');
                const fileInput = document.getElementById('fileInput');

                // File input change
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.handleFile(e.target.files[0]);
                    }
                });

                // Upload zone click
                uploadZone.addEventListener('click', () => {
                    fileInput.click();
                });

                // Drag and drop
                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.classList.add('dragover');
                });

                uploadZone.addEventListener('dragleave', () => {
                    uploadZone.classList.remove('dragover');
                });

                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadZone.classList.remove('dragover');
                    
                    if (e.dataTransfer.files.length > 0) {
                        this.handleFile(e.dataTransfer.files[0]);
                    }
                });

                // Progress updates
                document.addEventListener('parseProgress', (e) => {
                    this.updateProgress(e.detail.percent, e.detail.message);
                });

                // Download button
                document.getElementById('downloadBtn').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.downloadXML();
                });
            }

            async handleFile(file) {
                try {
                    // Enhanced file validation
                    this.validateFile(file);

                    this.currentFile = file;
                    this.showProgress();
                    this.hideError();
                    this.hideResults();
                    this.hideSuccess();

                    // Parse the file with detailed progress tracking
                    this.updateProgress(5, 'Reading file...');
                    await this.delay(100); // Allow UI to update

                    const parsedData = await this.parser.parseFile(file);

                    // Validate parsed data
                    this.validateParsedData(parsedData);

                    // Generate XML
                    this.updateProgress(95, 'Generating XML...');
                    await this.delay(100);

                    this.xmlContent = this.xmlGenerator.generateXML(parsedData);

                    // Validate XML output
                    this.validateXMLOutput();

                    // Show results
                    this.updateProgress(100, 'Conversion complete!');
                    await this.delay(500);

                    this.showResults();
                    this.hideProgress();
                    this.showSuccess();

                } catch (error) {
                    console.error('Conversion error:', error);
                    this.hideProgress();
                    this.showError(this.formatErrorMessage(error));
                }
            }

            validateFile(file) {
                if (!file) {
                    throw new Error('No file selected.');
                }

                if (!file.name.toLowerCase().endsWith('.rpt')) {
                    throw new Error('Invalid file type. Please select a Crystal Reports (.rpt) file.');
                }

                if (file.size === 0) {
                    throw new Error('The selected file is empty.');
                }

                if (file.size > 50 * 1024 * 1024) { // 50MB limit
                    throw new Error(`File size (${this.formatFileSize(file.size)}) exceeds the 50MB limit.`);
                }

                if (file.size < 100) {
                    throw new Error('File is too small to be a valid Crystal Reports file.');
                }
            }

            validateParsedData(parsedData) {
                if (!parsedData) {
                    throw new Error('Failed to parse RPT file. The file may be corrupted or use an unsupported format.');
                }

                if (!parsedData.metadata) {
                    throw new Error('Could not extract metadata from RPT file.');
                }

                // Warn about limited data extraction
                let warnings = [];
                if (!parsedData.fields || parsedData.fields.length === 0) {
                    warnings.push('No fields detected');
                }
                if (!parsedData.formulas || parsedData.formulas.length === 0) {
                    warnings.push('No formulas detected');
                }
                if (!parsedData.database || !parsedData.database.connections || parsedData.database.connections.length === 0) {
                    warnings.push('No database connections detected');
                }

                if (warnings.length > 0) {
                    console.warn('Parsing warnings:', warnings.join(', '));
                }
            }

            validateXMLOutput() {
                if (!this.xmlContent || this.xmlContent.length < 100) {
                    throw new Error('Generated XML is too short. Conversion may have failed.');
                }

                // Basic XML validation
                if (!this.xmlContent.includes('<?xml') || !this.xmlContent.includes('<CrystalReport>')) {
                    throw new Error('Generated XML appears to be malformed.');
                }
            }

            formatErrorMessage(error) {
                let message = error.message || 'An unknown error occurred.';

                // Add helpful suggestions based on error type
                if (message.includes('signature') || message.includes('format')) {
                    message += '\n\nSuggestions:\n• Ensure the file is a valid Crystal Reports (.rpt) file\n• Try opening the file in Crystal Reports to verify it\'s not corrupted\n• Check if the file was created with a very old or very new version of Crystal Reports';
                } else if (message.includes('size')) {
                    message += '\n\nSuggestions:\n• Try compressing the RPT file\n• Remove unnecessary elements from the report\n• Split large reports into smaller ones';
                } else if (message.includes('parse') || message.includes('read')) {
                    message += '\n\nSuggestions:\n• The file may be corrupted or password-protected\n• Try re-saving the file in Crystal Reports\n• Ensure the file is not currently open in another application';
                }

                return message;
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            updateProgress(percent, message) {
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                
                progressFill.style.width = `${percent}%`;
                progressText.textContent = message;
            }

            showProgress() {
                document.getElementById('progressContainer').style.display = 'block';
            }

            hideProgress() {
                document.getElementById('progressContainer').style.display = 'none';
            }

            showError(message) {
                const errorContainer = document.getElementById('errorContainer');
                const errorMessage = document.getElementById('errorMessage');
                
                errorMessage.textContent = message;
                errorContainer.style.display = 'block';
            }

            hideError() {
                document.getElementById('errorContainer').style.display = 'none';
            }

            showSuccess() {
                document.getElementById('successContainer').style.display = 'block';
                setTimeout(() => {
                    document.getElementById('successContainer').style.display = 'none';
                }, 5000);
            }

            hideSuccess() {
                document.getElementById('successContainer').style.display = 'none';
            }

            showResults() {
                const resultsContainer = document.getElementById('resultsContainer');
                const xmlPreview = document.getElementById('xmlPreview');
                
                // Show preview (truncated for display)
                const previewLength = 2000;
                const preview = this.xmlContent.length > previewLength 
                    ? this.xmlContent.substring(0, previewLength) + '\n\n... (truncated for preview)\n\nClick "Download XML" to get the complete file.'
                    : this.xmlContent;
                
                xmlPreview.textContent = preview;
                resultsContainer.style.display = 'block';
            }

            hideResults() {
                document.getElementById('resultsContainer').style.display = 'none';
            }

            downloadXML() {
                if (!this.xmlContent || !this.currentFile) {
                    this.showError('No XML content available for download.');
                    return;
                }

                try {
                    // Create blob with proper XML content type
                    const blob = new Blob([this.xmlContent], {
                        type: 'application/xml;charset=utf-8'
                    });
                    const url = URL.createObjectURL(blob);

                    // Generate filename
                    const originalName = this.currentFile.name.replace(/\.rpt$/i, '');
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                    const filename = `${originalName}_converted_${timestamp}.xml`;

                    // Create download link
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.style.display = 'none';

                    // Trigger download
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);

                    // Clean up
                    setTimeout(() => URL.revokeObjectURL(url), 1000);

                    // Show success message
                    this.showTemporaryMessage(`XML file "${filename}" downloaded successfully!`, 'success');

                } catch (error) {
                    console.error('Download error:', error);
                    this.showError('Failed to download XML file. Please try again.');
                }
            }

            showTemporaryMessage(message, type = 'info') {
                // Create temporary message element
                const messageDiv = document.createElement('div');
                messageDiv.className = `temporary-message ${type}`;
                messageDiv.textContent = message;
                messageDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#48bb78' : '#4299e1'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 1000;
                    font-weight: 500;
                    max-width: 300px;
                    word-wrap: break-word;
                `;

                document.body.appendChild(messageDiv);

                // Remove after 3 seconds
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 3000);
            }
        }

        // Browser compatibility checker
        class BrowserCompatibility {
            static check() {
                const issues = [];

                // Check for required APIs
                if (!window.FileReader) {
                    issues.push('FileReader API not supported');
                }
                if (!window.ArrayBuffer) {
                    issues.push('ArrayBuffer not supported');
                }
                if (!window.DataView) {
                    issues.push('DataView not supported');
                }
                if (!window.Blob) {
                    issues.push('Blob API not supported');
                }
                if (!window.URL || !window.URL.createObjectURL) {
                    issues.push('URL.createObjectURL not supported');
                }

                return {
                    compatible: issues.length === 0,
                    issues: issues
                };
            }

            static showCompatibilityWarning(issues) {
                const warningDiv = document.createElement('div');
                warningDiv.style.cssText = `
                    background: #fed7d7;
                    border: 1px solid #feb2b2;
                    color: #c53030;
                    padding: 20px;
                    margin: 20px;
                    border-radius: 8px;
                    font-weight: 500;
                `;
                warningDiv.innerHTML = `
                    <strong>Browser Compatibility Issues Detected:</strong><br>
                    ${issues.map(issue => `• ${issue}`).join('<br>')}
                    <br><br>
                    Please use a modern browser like Chrome, Firefox, Safari, or Edge.
                `;

                document.body.insertBefore(warningDiv, document.body.firstChild);
            }
        }

        // Initialize the converter when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Check browser compatibility
            const compatibility = BrowserCompatibility.check();

            if (!compatibility.compatible) {
                BrowserCompatibility.showCompatibilityWarning(compatibility.issues);
                return;
            }

            // Initialize the converter
            try {
                new RPTConverter();
                console.log('RPT to XML Converter initialized successfully');
            } catch (error) {
                console.error('Failed to initialize converter:', error);

                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    background: #fed7d7;
                    border: 1px solid #feb2b2;
                    color: #c53030;
                    padding: 20px;
                    margin: 20px;
                    border-radius: 8px;
                `;
                errorDiv.innerHTML = `
                    <strong>Initialization Error:</strong><br>
                    ${error.message}<br><br>
                    Please refresh the page and try again.
                `;

                document.body.appendChild(errorDiv);
            }
        });
    </script>
</body>
</html>
